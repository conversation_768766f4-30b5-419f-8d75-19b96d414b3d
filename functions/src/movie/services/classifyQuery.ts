// src/services/classifyQuery.ts
const fetch = (...args: any[]) => import('node-fetch').then(({default: fetch}) => fetch(...args as [any]));
import { Logger } from '../../common/utils/logger';

const GENRE_MAP: Record<string, number> = {
  action: 28,
  adventure: 12,
  animation: 16,
  comedy: 35,
  crime: 80,
  documentary: 99,
  drama: 18,
  family: 10751,
  fantasy: 14,
  history: 36,
  horror: 27,
  music: 10402,
  mystery: 9648,
  romance: 10749,
  'science fiction': 878,
  thriller: 53,
  war: 10752,
  western: 37
};

export class QueryClassifier {
  private huggingFaceToken: string;
  private logger: Logger;

  constructor() {
    this.logger = Logger.getInstance('query-classifier');
    this.huggingFaceToken = process.env.HUGGING_FACE_TOKEN!;
    
    if (!this.huggingFaceToken) {
      this.logger.error('HUGGING_FACE_TOKEN is not set in environment variables');
      throw new Error('HUGGING_FACE_TOKEN is required');
    }
    
    this.logger.info('QueryClassifier initialized');
  }

  async classify(query: string): Promise<string> {
    this.logger.debug('Classifying query', { query });
    const start = Date.now();
    
    try {
      // Check if query matches a genre
      const lowerQuery = query.toLowerCase();
      if (GENRE_MAP[lowerQuery]) {
        this.logger.debug('Query matched a genre', { 
          query, 
          genre: lowerQuery,
          executionTime: `${Date.now() - start}ms`
        });
        return 'genre';
      }

      const url = 'https://api-inference.huggingface.co/models/facebook/bart-large-mnli';
      const requestBody = {
        inputs: query,
        parameters: {
          candidate_labels: ['movie', 'tv', 'person', 'genre', 'multi']
        }
      };
      
      this.logger.debug('Sending classification request to Hugging Face', {
        query,
        url,
        model: 'facebook/bart-large-mnli'
      });
      
      // Add timeout to prevent hanging
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

      try {
        const response = await fetch(url, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.huggingFaceToken}`,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestBody),
          signal: controller.signal
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
        const errorText = await response.text().catch(() => 'Failed to read error response');
        this.logger.error('Classification API error', new Error(`Status: ${response.status}`), {
          query,
          status: response.status,
          statusText: response.statusText,
          error: errorText.substring(0, 200) // Log first 200 chars to avoid huge logs
        });
        return 'multi'; // Fallback to multi search on API error
      }
      
      const result = await response.json() as any;
      
      if (result.labels && result.labels.length > 0) {
        const classification = result.labels[0];
        const scores = result.scores || [];
        
        this.logger.debug('Classification result', {
          query,
          classification,
          scores: result.labels.map((label: string, i: number) => ({
            label,
            score: scores[i]?.toFixed(4)
          })),
          executionTime: `${Date.now() - start}ms`
        });
        
        return classification;
      }
      
      this.logger.debug('No classification labels returned, defaulting to multi', {
        query,
        result: JSON.stringify(result).substring(0, 200), // Log partial result
        executionTime: `${Date.now() - start}ms`
      });
      
      return 'multi';
      } catch (fetchError) {
        clearTimeout(timeoutId);
        if (fetchError instanceof Error && fetchError.name === 'AbortError') {
          this.logger.warn('Classification request timed out, falling back to multi search', {
            query,
            timeout: '5000ms',
            executionTime: `${Date.now() - start}ms`
          });
        } else {
          this.logger.error('Classification fetch error', fetchError, {
            query,
            executionTime: `${Date.now() - start}ms`
          });
        }
        return 'multi'; // Fallback to multi search on timeout or fetch error
      }
    } catch (error) {
      this.logger.error('Classification error', error, {
        query,
        executionTime: `${Date.now() - start}ms`
      });
      return 'multi'; // Fallback to multi search on error
    }
  }

  getGenreId(genre: string): number | null {
    const lowerGenre = genre.toLowerCase();
    const genreId = GENRE_MAP[lowerGenre] || null;
    
    if (genreId) {
      this.logger.debug('Found genre ID', { genre: lowerGenre, genreId });
    } else {
      this.logger.debug('No matching genre ID found', { genre: lowerGenre });
    }
    
    return genreId;
  }
}

